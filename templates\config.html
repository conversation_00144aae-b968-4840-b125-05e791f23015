{% extends "base.html" %}

{% block title %}Configuratie - <PERSON><PERSON>'s Baby Shop{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    <i class="bi bi-gear"></i> Configuratie Beheren
                </h3>
                <p class="mb-0 mt-2 opacity-75">
                    Hier kun je de keuze opties voor alle formulier velden aanpassen.
                </p>
            </div>
            <div class="card-body">
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle"></i>
                    <strong>Instructies:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Voer waarden in gescheiden door komma's</li>
                        <li>Spaties rond komma's worden automatisch weggehaald</li>
                        <li>Elk veld moet minimaal één waarde bevatten</li>
                        <li>Wijzigingen worden direct toegepast op nieuwe items</li>
                    </ul>
                </div>

                <form method="POST" id="configForm">
                    {{ form.hidden_tag() }}
                    
                    <!-- Geslacht Configuration -->
                    <div class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                {{ form.geslacht_values.label(class="form-label fw-bold") }}
                                <small class="text-muted d-block">Geslacht opties</small>
                            </div>
                            <div class="col-md-9">
                                {{ form.geslacht_values(class="form-control") }}
                                {% if form.geslacht_values.description %}
                                    <div class="form-text">{{ form.geslacht_values.description }}</div>
                                {% endif %}
                                {% if form.geslacht_values.errors %}
                                    <div class="text-danger">
                                        {% for error in form.geslacht_values.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Maat Configuration -->
                    <div class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                {{ form.maat_values.label(class="form-label fw-bold") }}
                                <small class="text-muted d-block">Beschikbare maten</small>
                            </div>
                            <div class="col-md-9">
                                {{ form.maat_values(class="form-control") }}
                                {% if form.maat_values.description %}
                                    <div class="form-text">{{ form.maat_values.description }}</div>
                                {% endif %}
                                {% if form.maat_values.errors %}
                                    <div class="text-danger">
                                        {% for error in form.maat_values.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Brand Configuration -->
                    <div class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                {{ form.brand_values.label(class="form-label fw-bold") }}
                                <small class="text-muted d-block">Beschikbare merken</small>
                            </div>
                            <div class="col-md-9">
                                {{ form.brand_values(class="form-control") }}
                                {% if form.brand_values.description %}
                                    <div class="form-text">{{ form.brand_values.description }}</div>
                                {% endif %}
                                {% if form.brand_values.errors %}
                                    <div class="text-danger">
                                        {% for error in form.brand_values.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Category Configuration -->
                    <div class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                {{ form.category_values.label(class="form-label fw-bold") }}
                                <small class="text-muted d-block">Beschikbare categorieën</small>
                            </div>
                            <div class="col-md-9">
                                {{ form.category_values(class="form-control") }}
                                {% if form.category_values.description %}
                                    <div class="form-text">{{ form.category_values.description }}</div>
                                {% endif %}
                                {% if form.category_values.errors %}
                                    <div class="text-danger">
                                        {% for error in form.category_values.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-success btn-lg") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="{{ url_for('manage') }}" class="btn btn-secondary btn-lg">
                                    <i class="bi bi-arrow-left"></i> Terug naar Beheren
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="card mt-4 shadow">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-eye"></i> Huidige Configuratie Preview
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h6 class="fw-bold text-primary">Geslacht</h6>
                        <div id="geslacht-preview" class="preview-tags"></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="fw-bold text-primary">Maten</h6>
                        <div id="maat-preview" class="preview-tags"></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="fw-bold text-primary">Merken</h6>
                        <div id="brand-preview" class="preview-tags"></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="fw-bold text-primary">Categorieën</h6>
                        <div id="category-preview" class="preview-tags"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to update preview
    function updatePreview() {
        const fields = ['geslacht', 'maat', 'brand', 'category'];
        
        fields.forEach(field => {
            const textarea = document.querySelector(`textarea[name="${field}_values"]`);
            const preview = document.getElementById(`${field}-preview`);
            
            if (textarea && preview) {
                const values = textarea.value.split(',').map(v => v.trim()).filter(v => v);
                preview.innerHTML = values.map(value => 
                    `<span class="badge bg-primary me-1 mb-1">${value}</span>`
                ).join('');
                
                if (values.length === 0) {
                    preview.innerHTML = '<span class="text-muted">Geen waarden</span>';
                }
            }
        });
    }
    
    // Update preview on page load
    updatePreview();
    
    // Update preview when textareas change
    document.querySelectorAll('textarea').forEach(textarea => {
        textarea.addEventListener('input', updatePreview);
    });
    
    // Form validation
    document.getElementById('configForm').addEventListener('submit', function(e) {
        const textareas = document.querySelectorAll('textarea');
        let hasError = false;
        
        textareas.forEach(textarea => {
            const values = textarea.value.split(',').map(v => v.trim()).filter(v => v);
            if (values.length === 0) {
                hasError = true;
                textarea.classList.add('is-invalid');
            } else {
                textarea.classList.remove('is-invalid');
            }
        });
        
        if (hasError) {
            e.preventDefault();
            alert('Alle velden moeten minimaal één waarde bevatten.');
        }
    });
});
</script>

<style>
.preview-tags {
    min-height: 2rem;
    padding: 0.5rem;
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 0.375rem;
}

.preview-tags .badge {
    font-size: 0.8rem;
}

textarea.is-invalid {
    border-color: #dc3545;
}
</style>
{% endblock %}
