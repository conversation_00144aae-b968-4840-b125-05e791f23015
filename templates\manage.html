{% extends "admin_base.html" %}

{% block title %}Beheren - <PERSON><PERSON>'s Baby Shop{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-table"></i> Items Beheren</h2>
    <a href="{{ url_for('new_item') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Nieuw Item
    </a>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-10">
                <input type="text" name="search" class="form-control" placeholder="Zoeken op ID, brand, category, geslacht of maat..." value="{{ search_query }}">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search"></i> <PERSON>ken
                </button>
            </div>
        </form>
        {% if search_query %}
            <div class="mt-2">
                <small class="text-muted">Zoekresultaten voor: "{{ search_query }}"</small>
                <a href="{{ url_for('manage') }}" class="btn btn-sm btn-outline-secondary ms-2">
                    <i class="bi bi-x"></i> Wissen
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Items Table -->
{% if items %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Foto</th>
                            <th>ID</th>
                            <th>Geslacht</th>
                            <th>Maat</th>
                            <th>Prijs</th>
                            <th>Brand</th>
                            <th>Category</th>
                            <th>Toegevoegd</th>
                            <th>Acties</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td>
                                {% if item.foto_filename %}
                                    <img src="{{ url_for('static', filename='uploads/' + item.foto_filename) }}" 
                                         class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;"
                                         data-bs-toggle="modal" data-bs-target="#imageModal{{ loop.index }}"
                                         style="cursor: pointer;">
                                {% else %}
                                    <div class="bg-secondary rounded d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="bi bi-image text-muted"></i>
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                <code class="text-primary">{{ item.id[:8] }}...</code>
                                <button class="btn btn-sm btn-outline-secondary ms-1" 
                                        onclick="copyToClipboard('{{ item.id }}')" 
                                        title="Kopieer volledige ID">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'primary' if item.geslacht == 'jongen' else 'danger' }}">
                                    {{ item.geslacht }}
                                </span>
                            </td>
                            <td><span class="badge bg-secondary">{{ item.maat }}</span></td>
                            <td><strong>€{{ "%.2f"|format(item.prijs) }}</strong></td>
                            <td>{{ item.brand }}</td>
                            <td>{{ item.category }}</td>
                            <td>
                                <small class="text-muted">
                                    {{ item.created_at.strftime('%d-%m-%Y %H:%M') if item.created_at else 'Onbekend' }}
                                </small>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-danger" 
                                        onclick="confirmDelete('{{ item.id }}', '{{ item.brand }} {{ item.category }}')"
                                        title="Verwijderen">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        
                        <!-- Image Modal -->
                        {% if item.foto_filename %}
                        <div class="modal fade" id="imageModal{{ loop.index }}" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{{ item.brand }} {{ item.category }}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body text-center">
                                        <img src="{{ url_for('static', filename='uploads/' + item.foto_filename) }}" 
                                             class="img-fluid rounded">
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="mt-3">
                <small class="text-muted">Totaal: {{ items|length }} item(s)</small>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-inbox display-1 text-muted"></i>
        <h3 class="mt-3">Geen items gevonden</h3>
        <p class="text-muted">
            {% if search_query %}
                Geen items gevonden voor "{{ search_query }}".
            {% else %}
                Voeg je eerste item toe om te beginnen.
            {% endif %}
        </p>
        <a href="{{ url_for('new_item') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Nieuw Item Toevoegen
        </a>
    </div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Item Verwijderen</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Weet je zeker dat je dit item wilt verwijderen?</p>
                <p><strong id="deleteItemName"></strong></p>
                <p class="text-muted">Deze actie kan niet ongedaan worden gemaakt.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuleren</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Verwijderen</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show a temporary success message
        const toast = document.createElement('div');
        toast.className = 'toast-container position-fixed top-0 end-0 p-3';
        toast.innerHTML = `
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">Gekopieerd!</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">ID gekopieerd naar klembord</div>
            </div>
        `;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    });
}

function confirmDelete(itemId, itemName) {
    document.getElementById('deleteItemName').textContent = itemName;
    const deleteUrl = `/delete/${itemId}`;

    const confirmBtn = document.getElementById('confirmDeleteBtn');
    confirmBtn.href = deleteUrl;

    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
