from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

db = SQLAlchemy()

class Item(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    geslacht = db.Column(db.String(20), nullable=False)
    maat = db.Column(db.String(10), nullable=False)
    prijs = db.Column(db.Float, nullable=False)
    brand = db.Column(db.String(50), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    foto_filename = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Item {self.id}: {self.brand} {self.category}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'geslacht': self.geslacht,
            'maat': self.maat,
            'prijs': self.prijs,
            'brand': self.brand,
            'category': self.category,
            'foto_filename': self.foto_filename,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
