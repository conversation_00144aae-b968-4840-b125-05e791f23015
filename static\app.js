// Tita's Baby Shop - JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Camera functionality
    const cameraBtn = document.getElementById('cameraBtn');
    const cameraSection = document.getElementById('cameraSection');
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const captureBtn = document.getElementById('captureBtn');
    const stopCameraBtn = document.getElementById('stopCameraBtn');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const removeImageBtn = document.getElementById('removeImageBtn');
    const fileInput = document.querySelector('input[type="file"]');
    
    let stream = null;
    let capturedImageBlob = null;

    // Check if camera elements exist (only on new item page)
    if (cameraBtn && video && canvas) {
        
        // Start camera
        cameraBtn.addEventListener('click', async function() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        facingMode: 'environment' // Use back camera on mobile
                    } 
                });
                video.srcObject = stream;
                cameraSection.style.display = 'block';
                cameraBtn.style.display = 'none';
            } catch (err) {
                console.error('Error accessing camera:', err);
                alert('Kan camera niet openen. Controleer of je toestemming hebt gegeven voor camera toegang.');
            }
        });

        // Capture photo
        captureBtn.addEventListener('click', function() {
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0);
            
            // Convert canvas to blob
            canvas.toBlob(function(blob) {
                capturedImageBlob = blob;
                
                // Show preview
                const url = URL.createObjectURL(blob);
                previewImg.src = url;
                imagePreview.style.display = 'block';
                
                // Create a File object from the blob
                const file = new File([blob], 'camera-capture.jpg', { type: 'image/jpeg' });
                
                // Create a new FileList and assign it to the input
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
                
                // Stop camera
                stopCamera();
            }, 'image/jpeg', 0.8);
        });

        // Stop camera
        stopCameraBtn.addEventListener('click', stopCamera);

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
            cameraSection.style.display = 'none';
            cameraBtn.style.display = 'block';
        }

        // Remove captured image
        removeImageBtn.addEventListener('click', function() {
            imagePreview.style.display = 'none';
            fileInput.value = '';
            capturedImageBlob = null;
            if (previewImg.src) {
                URL.revokeObjectURL(previewImg.src);
            }
        });

        // Handle file input change (for regular file uploads)
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const url = URL.createObjectURL(file);
                previewImg.src = url;
                imagePreview.style.display = 'block';
            }
        });
    }

    // Form validation
    const itemForm = document.getElementById('itemForm');
    if (itemForm) {
        itemForm.addEventListener('submit', function(e) {
            const prijs = document.querySelector('input[name="prijs"]');
            if (prijs && (parseFloat(prijs.value) <= 0 || isNaN(parseFloat(prijs.value)))) {
                e.preventDefault();
                alert('Voer een geldige prijs in (groter dan 0).');
                prijs.focus();
                return false;
            }
        });
    }

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });

    // Add loading state to form submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('input[type="submit"], button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.textContent;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Bezig...';
                
                // Re-enable after 10 seconds as fallback
                setTimeout(function() {
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                }, 10000);
            }
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(function(card, index) {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in');
    });
});

// Utility function for copying text to clipboard
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            showToast('Gekopieerd!', 'ID gekopieerd naar klembord');
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// Fallback for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('Gekopieerd!', 'ID gekopieerd naar klembord');
        }
    } catch (err) {
        console.error('Fallback: Could not copy text: ', err);
    }
    
    document.body.removeChild(textArea);
}

// Show toast notification
function showToast(title, message) {
    const toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    
    toastContainer.innerHTML = `
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">${message}</div>
        </div>
    `;
    
    document.body.appendChild(toastContainer);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        toastContainer.remove();
    }, 3000);
    
    // Handle close button
    const closeBtn = toastContainer.querySelector('.btn-close');
    closeBtn.addEventListener('click', () => {
        toastContainer.remove();
    });
}
