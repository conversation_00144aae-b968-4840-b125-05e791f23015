from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import SelectField, FloatField, SubmitField, StringField, TextAreaField, FieldList, FormField, PasswordField
from wtforms.validators import DataRequired, NumberRange
import json
import os

def load_config():
    """Load configuration from config.json"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

class ItemForm(FlaskForm):
    def __init__(self, *args, **kwargs):
        super(ItemForm, self).__init__(*args, **kwargs)
        
        # Load choices from config
        config = load_config()
        
        self.geslacht.choices = [(choice, choice) for choice in config['geslacht']]
        self.maat.choices = [(choice, choice) for choice in config['maat']]
        self.brand.choices = [(choice, choice) for choice in config['brand']]
        self.category.choices = [(choice, choice) for choice in config['category']]
    
    geslacht = SelectField('Geslacht', validators=[DataRequired()])
    maat = SelectField('Maat', validators=[DataRequired()])
    prijs = FloatField('Prijs (€)', validators=[DataRequired(), NumberRange(min=0.01)])
    brand = SelectField('Brand', validators=[DataRequired()])
    category = SelectField('Category', validators=[DataRequired()])
    foto = FileField('Foto', validators=[FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Alleen afbeeldingen toegestaan!')])
    submit = SubmitField('Item Toevoegen')

class SearchForm(FlaskForm):
    search = StringField('Zoeken...')
    submit = SubmitField('Zoeken')

class ConfigForm(FlaskForm):
    def __init__(self, *args, **kwargs):
        super(ConfigForm, self).__init__(*args, **kwargs)

        # Only set default values if no data is provided (GET request)
        if not self.is_submitted():
            # Load current config
            config = load_config()

            # Set default values from config
            self.geslacht_values.data = ', '.join(config.get('geslacht', []))
            self.maat_values.data = ', '.join(config.get('maat', []))
            self.brand_values.data = ', '.join(config.get('brand', []))
            self.category_values.data = ', '.join(config.get('category', []))

    geslacht_values = TextAreaField(
        'Geslacht Opties',
        validators=[DataRequired()],
        description='Voer waarden in gescheiden door komma\'s (bijv: jongen, meisje)',
        render_kw={'rows': 3, 'placeholder': 'jongen, meisje'}
    )

    maat_values = TextAreaField(
        'Maat Opties',
        validators=[DataRequired()],
        description='Voer waarden in gescheiden door komma\'s (bijv: 50, 56, 62, 68)',
        render_kw={'rows': 4, 'placeholder': '50, 56, 62, 68, 74, 80, 86, 92, 98, 104, 110, 116, 122, 128, 134, 140, 146, 152, 158, 164, 170, 176'}
    )

    brand_values = TextAreaField(
        'Brand Opties',
        validators=[DataRequired()],
        description='Voer waarden in gescheiden door komma\'s (bijv: H&M, Zara, C&A)',
        render_kw={'rows': 4, 'placeholder': 'H&M, Zara, C&A, Primark, Next'}
    )

    category_values = TextAreaField(
        'Category Opties',
        validators=[DataRequired()],
        description='Voer waarden in gescheiden door komma\'s (bijv: sweater, T-shirt, broek)',
        render_kw={'rows': 4, 'placeholder': 'sweater, T-shirt, broek, hemd, jurk, rok, short, jas, vest'}
    )

    submit = SubmitField('Configuratie Opslaan')

    def get_config_dict(self):
        """Convert form data back to config dictionary format"""
        return {
            'geslacht': [item.strip() for item in self.geslacht_values.data.split(',') if item.strip()],
            'maat': [item.strip() for item in self.maat_values.data.split(',') if item.strip()],
            'brand': [item.strip() for item in self.brand_values.data.split(',') if item.strip()],
            'category': [item.strip() for item in self.category_values.data.split(',') if item.strip()]
        }

class LoginForm(FlaskForm):
    username = StringField('Gebruikersnaam', validators=[DataRequired()], render_kw={'placeholder': 'Voer gebruikersnaam in'})
    password = PasswordField('Wachtwoord', validators=[DataRequired()], render_kw={'placeholder': 'Voer wachtwoord in'})
    submit = SubmitField('Inloggen')
