/* Custom Dark Theme for <PERSON><PERSON>'s Baby Shop */

:root {
    --bs-primary: #6f42c1;
    --bs-primary-rgb: 111, 66, 193;
    --bs-secondary: #6c757d;
    --bs-success: #198754;
    --bs-danger: #dc3545;
    --bs-warning: #ffc107;
    --bs-info: #0dcaf0;
    --bs-light: #f8f9fa;
    --bs-dark: #212529;
}

/* Body and general styling */
body {
    background-color: #1a1a1a;
    color: #e9ecef;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* Cards */
.card {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
}

.card-header {
    background-color: var(--bs-primary);
    border-bottom: 1px solid #404040;
}

/* Forms */
.form-control,
.form-select {
    background-color: #3d3d3d;
    border: 1px solid #555;
    color: #e9ecef;
}

.form-control:focus,
.form-select:focus {
    background-color: #3d3d3d;
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    color: #e9ecef;
}

.form-control::placeholder {
    color: #adb5bd;
}

/* Input groups */
.input-group-text {
    background-color: #3d3d3d;
    border: 1px solid #555;
    color: #e9ecef;
}

/* Buttons */
.btn-primary {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.btn-primary:hover {
    background-color: #5a359a;
    border-color: #5a359a;
}

.btn-outline-primary {
    color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.btn-outline-primary:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: #fff;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

/* Tables */
.table {
    color: #e9ecef;
}

.table-dark {
    --bs-table-bg: #2d2d2d;
    --bs-table-border-color: #404040;
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Badges */
.badge {
    font-size: 0.8em;
}

/* Modals */
.modal-content {
    background-color: #2d2d2d;
    border: 1px solid #404040;
}

.modal-header {
    border-bottom: 1px solid #404040;
}

.modal-footer {
    border-top: 1px solid #404040;
}

/* Alerts */
.alert {
    border: none;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.2);
    color: #75b798;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    color: #ea868f;
}

/* Footer */
footer {
    background-color: #1a1a1a !important;
    border-top: 1px solid #404040;
}

/* Image thumbnails */
.img-thumbnail {
    background-color: #3d3d3d;
    border: 1px solid #555;
}

/* Video element for camera */
video {
    background-color: #000;
    border: 2px solid #555;
}

/* Custom animations */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-sm {
        font-size: 0.75rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Toast styling */
.toast {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    color: #e9ecef;
}

.toast-header {
    background-color: #3d3d3d;
    border-bottom: 1px solid #404040;
    color: #e9ecef;
}

/* Loading spinner */
.spinner-border {
    color: var(--bs-primary);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #666;
}
